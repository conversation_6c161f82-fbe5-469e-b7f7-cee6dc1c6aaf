# TCGA ATAC-seq Download Instructions

## 🎯 Overview
This pipeline downloads ATAC-seq data for 6 new cancer types from TCGA and integrates them with your existing CA_RT data.

## 📋 Prerequisites

### 1. Install Python Dependencies
```bash
pip install -r requirements_tcga.txt
```

### 2. Install GDC Data Transfer Tool (Recommended)
- Download from: https://gdc.cancer.gov/access-data/gdc-data-transfer-tool
- Or use manual download from TCGA Data Portal

## 🚀 Step-by-Step Process

### Step 1: Download TCGA ATAC-seq Data
```bash
# Run the download script
./download_tcga_atac.sh
```

**Alternative: Manual Download**
1. Go to https://portal.gdc.cancer.gov/
2. Select "Files" tab
3. Filter by:
   - Data Type: ATAC-seq
   - Project: TCGA-SKCM, TCGA-ESCA, TCGA-LIHC, TCGA-STAD, TCGA-HNSC, TCGA-THCA, TCGA-UCEC, TCGA-CESC
4. Download files to appropriate directories

### Step 2: Extract Signals for Your Genomic Regions
```bash
# Process downloaded files
python3 extract_tcga_regions.py
```

### Step 3: Integrate with Existing Data
```bash
# Merge with your current CA_RT data
python3 integrate_tcga_data.py
```

## 📁 Directory Structure
```
tcga_downloads/
├── metadata/          # API responses and file lists
├── atac_seq/          # Downloaded ATAC-seq files
│   ├── skin/
│   ├── esophagus/
│   ├── liver/
│   ├── stomach/
│   ├── head_neck/
│   └── uterus/
├── processed/         # Processed data for your regions
└── logs/              # Processing logs
```

## 🎯 Cancer Types Being Downloaded

1. **Skin (SKCM)**: Melanoma - 11 samples
2. **Esophagus (ESCA)**: Esophageal carcinoma - 15 samples  
3. **Liver (LIHC)**: Hepatocellular carcinoma - 17 samples
4. **Stomach (STAD)**: Stomach adenocarcinoma - 20 samples
5. **Head/Neck (HNSC+THCA)**: Head/neck + thyroid cancers - 21 samples
6. **Uterus (UCEC+CESC)**: Endometrial + cervical cancers - 13 samples

## ⚠️ Important Notes

- **File sizes**: ATAC-seq files can be large (100MB-1GB each)
- **Processing time**: Extracting signals may take several hours
- **Storage**: Ensure you have sufficient disk space (~10-50GB)
- **Internet**: Stable connection required for downloads

## 🔧 Troubleshooting

### Download Issues
- Check internet connection
- Verify TCGA portal access
- Try manual download if automated fails

### Processing Issues
- Ensure pyBigWig is installed correctly
- Check file paths and permissions
- Verify genomic coordinate formats

## 📊 Expected Output

After completion, you'll have:
- **CA_RT_10KB_with_new_cancers.tsv**: Integrated data with 6 new cancer types
- **Individual cancer files**: Processed ATAC-seq data for each cancer type
- **Metadata files**: Information about downloaded samples

## 🎉 Next Steps

Once integrated, you can:
1. Apply blacklist removal to new cancer data
2. Generate 100KB and 1MB aggregated files
3. Perform cancer vs normal analysis for 13 cancer types total
4. Correlate with your mutation data

## 📞 Support

If you encounter issues:
1. Check the logs in tcga_downloads/logs/
2. Verify file formats and paths
3. Ensure all dependencies are installed
