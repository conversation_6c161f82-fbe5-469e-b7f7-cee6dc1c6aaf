# plot_ca_rt_mut_same_x_v2.R
suppressPackageStartupMessages({
  library(data.table); library(dplyr); library(tidyr); library(ggplot2)
  library(zoo); library(matrixStats); library(patchwork)
})

# ---------- helpers ----------
okabe_ito <- c(blue="#0072B2", green="#009E73", purple="#CC79A7", orange="#E69F00")

as_chrnum <- function(chr) {
  x <- gsub("^chr","", as.character(chr))
  suppressWarnings(as.integer(replace(x, x %in% c("X","x"), 23)))
}

roll_med <- function(x, k=31) if (k<=1) x else zoo::rollmedian(x, k=k, fill=NA, align="center")
winsor <- function(x, p=99) { if (is.na(p)) return(x); hi <- quantile(x, p/100, na.rm=TRUE); x <- as.numeric(x); x[x>hi] <- hi; x }
mut_xform <- function(x, mode=c("log1p","sqrt","none")[1]) if (mode=="log1p") log1p(pmax(x,0)) else if (mode=="sqrt") sqrt(pmax(x,0)) else x

guess_value_col <- function(dt) {
  cand <- c("mutation_rate","mut_rate","mut_per_mb","mut_per_1mb","snvs_per_mb","snvs_per_mbp",
            "snv_density","indels_per_mb","indel_density","value")
  lc <- tolower(names(dt))
  for (nm in cand) {
    i <- which(lc==nm | grepl(nm, lc, fixed=TRUE)); if (length(i)) { col <- names(dt)[i[1]]; if (is.numeric(dt[[col]])) return(col) }
  }
  core <- tolower(c("chr","chrom","chromosome","start","start_pos","start_bp","end","end_pos","end_bp"))
  for (nm in names(dt)) if (!(tolower(nm) %in% core) && is.numeric(dt[[nm]])) return(nm)
  stop("Could not guess value column. Columns: ", paste(names(dt), collapse=", "))
}

read_mut <- function(path) {
  dt <- fread(path)
  lc <- tolower(names(dt))
  chr   <- if ("chr" %in% lc) names(dt)[match("chr", lc)] else names(dt)[which.max(grepl("chrom|chr", lc))]
  start <- names(dt)[which.max(grepl("start", lc))]
  end   <- names(dt)[which.max(grepl("end", lc))]
  val   <- guess_value_col(dt)
  dt <- dt[, .(chr=as.character(get(chr)), start=as.integer(get(start)), end=as.integer(get(end)), value=as.numeric(get(val)))]
  # collapse accidental duplicates per window
  dt[, .(value = median(value, na.rm=TRUE)), by=.(chr,start,end)]
}

make_zebra <- function(chr_vec) {
  n <- length(chr_vec)
  idx <- c(1L, which(chr_vec[-1L] != chr_vec[-n]) + 1L, n + 1L)
  data.frame(xmin=idx[-length(idx)], xmax=idx[-1]-1, chr=chr_vec[idx[-length(idx)]], odd=seq_along(idx[-1])%%2==1)
}

# ---------- main ----------
plot_ca_rt_mut_same_x <- function(
    ca_rt_path = "new_ca_rt_mutation/CA_RT_1MB.tsv",
    snv_path   = "new_ca_rt_mutation/HMF_snv_1MB.csv",
    indel_path = "new_ca_rt_mutation/HMF_indel_1MB.csv",
    out_prefix = "plots/CA_RT_SNV_INDEL_1MB_sharedX",
    rt_regex   = "(?i)(rt|repliseq|replication)",
    autosomes_only = TRUE,           # keep chr 1–22 only
    label_every = 1,                 # label every chromosome (set 2 for 1,3,5,...)
    smooth_ca=31, smooth_rt=31, smooth_mut=31,
    winsor_pct=99, mut_transform="log1p",
    width_in=7.2, height_in=4.6, dpi=600, use_cairo_pdf=FALSE
){
  dir.create(dirname(out_prefix), recursive=TRUE, showWarnings=FALSE)
  
  # ---- read CA+RT and collapse duplicate rows by median (per window) ----
  hdr <- fread(ca_rt_path, nrows=0, sep="\t")
  stopifnot(ncol(hdr) >= 4)
  coord_cols <- names(hdr)[1:3]
  track_cols <- names(hdr)[-(1:3)]
  rt_mask <- grepl(rt_regex, track_cols, perl=TRUE)
  ca_cols <- track_cols[!rt_mask]; rt_cols <- track_cols[rt_mask]
  
  dt <- fread(ca_rt_path, sep="\t", select=c(coord_cols, ca_cols, rt_cols), showProgress=FALSE)
  setDT(dt); setnames(dt, coord_cols, c("chr","start","end"))
  
  # optional: keep autosomes 1–22 only
  if (isTRUE(autosomes_only)) dt <- dt[as_chrnum(chr) %in% 1:22]
  
  # collapse any duplicate coordinate rows by median across duplicates (per column)
  if (any(duplicated(dt[, .(chr,start,end)]))) {
    message("[CA_RT] Found duplicate windows; collapsing by median per column.")
    dt <- dt[, lapply(.SD, function(v) suppressWarnings(median(as.numeric(v), na.rm=TRUE))), by=.(chr,start,end)]
  }
  
  # order by chr numeric then start
  dt[, chr_num := as_chrnum(chr)]
  setorder(dt, chr_num, start)
  dt[, chr_num := NULL]
  
  # row-wise medians for CA and RT (now one value per window)
  med_ca <- if (length(ca_cols)) rowMedians(as.matrix(dt[, ..ca_cols]), na.rm=TRUE) else rep(NA_real_, nrow(dt))
  med_rt <- if (length(rt_cols)) rowMedians(as.matrix(dt[, ..rt_cols]), na.rm=TRUE) else rep(NA_real_, nrow(dt))
  
  coord_dt <- dt[, .(chr, start, end)]
  nwin <- nrow(coord_dt)
  
  # ---- read mutations and enforce the SAME window set/order as CA_RT ----
  snv <- read_mut(snv_path); ind <- read_mut(indel_path)
  if (isTRUE(autosomes_only)) {
    snv <- snv[as_chrnum(chr) %in% 1:22]
    ind <- ind[as_chrnum(chr) %in% 1:22]
  }
  
  # Must match sets exactly; stop if not (safer than silently merging)
  key_ca  <- paste(coord_dt$chr, coord_dt$start, coord_dt$end)
  key_snv <- paste(snv$chr,     snv$start,     snv$end)
  key_ind <- paste(ind$chr,     ind$start,     ind$end)
  if (!setequal(unique(key_ca), unique(key_snv)) || !setequal(unique(key_ca), unique(key_ind))) {
    stop("Genomic windows differ across files. Please regenerate tables so all share identical (chr,start,end).")
  }
  # reorder snv/indel to CA order
  snv <- snv[match(key_ca, paste(snv$chr, snv$start, snv$end))]
  ind <- ind[match(key_ca, paste(ind$chr, ind$start, ind$end))]
  
  # ---- preprocess on aligned x-axis ----
  prep <- function(x, k, p) roll_med(winsor(as.numeric(x), p), k)
  med_ca_s <- prep(med_ca, smooth_ca, winsor_pct)
  med_rt_s <- prep(med_rt, smooth_rt, winsor_pct)
  snv_s    <- prep(mut_xform(snv$value,  mut_transform), smooth_mut, winsor_pct)
  ind_s    <- prep(mut_xform(ind$value,  mut_transform), smooth_mut, winsor_pct)
  
  # ---- long format ----
  x <- seq_len(nwin)
  top_df <- tibble::tibble(x=x, CA=med_ca_s, RT=med_rt_s) |>
    pivot_longer(c("CA","RT"), names_to="track", values_to="value") |>
    filter(!is.na(value))
  bot_df <- tibble::tibble(x=x, SNV=snv_s, INDEL=ind_s) |>
    pivot_longer(c("SNV","INDEL"), names_to="track", values_to="value") |>
    filter(!is.na(value))
  
  # ---- chromosome zebra + tick positions (labels 1..22) ----
  chr_vec <- as.character(coord_dt$chr)
  zebra <- make_zebra(chr_vec)
  mids <- floor((zebra$xmin + zebra$xmax)/2)
  labs <- gsub("^chr","", zebra$chr)
  keep <- seq_along(mids) %% label_every == 1
  tick_idx  <- mids[keep]
  tick_labs <- labs[keep]
  
  theme_pub <- theme_classic(base_size=9) +
    theme(axis.title.x = element_text(margin=margin(t=6)),
          axis.title.y = element_text(margin=margin(r=6)),
          axis.text.x  = element_text(size=8),
          axis.text.y  = element_text(size=8),
          plot.title   = element_text(face="bold", hjust=0),
          legend.position = "top",
          legend.title = element_blank(),
          legend.key.height = unit(0.3,"lines"),
          legend.key.width  = unit(1.0,"lines"),
          plot.margin = margin(5.5,5.5,5.5,5.5))
  
  # dynamic palettes
  top_cols <- c(CA=okabe_ito["blue"], RT=okabe_ito["green"])[unique(top_df$track)]
  bot_cols <- c(SNV=okabe_ito["purple"], INDEL=okabe_ito["orange"])[unique(bot_df$track)]
  
  # ---- plots (both panels share the same chr ticks) ----
  p_top <- ggplot() +
    geom_rect(data=zebra, aes(xmin=xmin, xmax=xmax, ymin=-Inf, ymax=Inf, fill=odd),
              inherit.aes=FALSE, alpha=0.06, color=NA) +
    scale_fill_manual(values=c("TRUE"="grey90","FALSE"="white"), guide="none") +
    geom_line(data=top_df, aes(x=x, y=value, color=track), linewidth=0.5, alpha=0.95) +
    scale_color_manual(values=top_cols) +
    scale_x_continuous(breaks=tick_idx, labels=tick_labs, expand=expansion(mult=c(0,0.005))) +
    labs(x=NULL, y="CA / RT (median)", title="CA and RT medians (1 Mb windows)") +
    theme_pub
  
  ylab_mut <- switch(mut_transform, log1p="Mutation rate (log1p)", sqrt="Mutation rate (sqrt)", "Mutation rate")
  p_bot <- ggplot() +
    geom_rect(data=zebra, aes(xmin=xmin, xmax=xmax, ymin=-Inf, ymax=Inf, fill=odd),
              inherit.aes=FALSE, alpha=0.06, color=NA) +
    scale_fill_manual(values=c("TRUE"="grey90","FALSE"="white"), guide="none") +
    geom_line(data=bot_df, aes(x=x, y=value, color=track), linewidth=0.5, alpha=0.95) +
    scale_color_manual(values=bot_cols) +
    scale_x_continuous(breaks=tick_idx, labels=tick_labs, expand=expansion(mult=c(0,0.005))) +
    labs(x="Genome windows (sorted by chr,start)", y=ylab_mut, title="HMF SNV and INDEL (1 Mb windows)") +
    theme_pub
  
  p <- (p_top / p_bot) + plot_layout(heights=c(1,1.15)) +
    plot_annotation(tag_levels="A", theme=theme(plot.title=element_text(face="bold")))
  
  png_file <- paste0(out_prefix, ".png")
  pdf_file <- paste0(out_prefix, ".pdf")
  ggsave(png_file, plot=p, width=width_in, height=height_in, dpi=dpi, bg="white")
  ggsave(pdf_file, plot=p, width=width_in, height=height_in,
         device = if (isTRUE(use_cairo_pdf)) cairo_pdf else pdf, bg="white")
  message("Saved:\n  - ", png_file, "\n  - ", pdf_file)
}
