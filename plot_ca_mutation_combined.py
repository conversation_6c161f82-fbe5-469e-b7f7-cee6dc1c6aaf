#!/usr/bin/env python3
import os
import argparse
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

sns.set_theme(style="white", context="paper", font_scale=1.2)

# ---------- Helpers to load IDs ----------

def load_ids(ann_path):
    ann = pd.read_csv(ann_path, sep='\t')
    is_rt = ann.get('data_type', '').eq('RepliSeq') if 'data_type' in ann.columns else pd.Series(False, index=ann.index)
    ca_mask = ~is_rt
    bc = ann.get('biosample_category', '').astype(str).str.lower()
    src = ann.get('source', '').astype(str)
    cond = ann.get('condition', '').astype(str)
    ca_cancer = ca_mask & (
        bc.str.contains('tumor', na=False) |
        src.isin(['TCGA', 'Rendeiro_CLL']) |
        cond.str.contains('cancer', case=False, na=False)
    )
    ca_normal = ca_mask & ~ca_cancer
    return {
        'ca_cancer': set(ann.loc[ca_cancer, 'sample_id']),
        'ca_normal': set(ann.loc[ca_normal, 'sample_id']),
        'rt': set(ann.loc[is_rt, 'sample_id'])
    }

# ---------- CA median track ----------

def header_and_keep_indices(matrix_path, include_samples, exclude_samples):
    with open(matrix_path, 'r') as f:
        header = f.readline().rstrip('\n').split('\t')
    coords = header[:3]
    samples = header[3:]
    keep_names = [s for s in samples if (s in include_samples) and (s not in exclude_samples)]
    usecols = coords + keep_names
    return usecols, coords, keep_names


def median_track(matrix_path, include_samples, exclude_samples=None, chunksize=20000):
    exclude_samples = exclude_samples or set()
    usecols, coords, keep_names = header_and_keep_indices(matrix_path, include_samples, exclude_samples)
    if len(keep_names) == 0:
        raise ValueError(f"No overlapping samples found to compute median in {matrix_path}")

    med_vals = []
    chr_vals = []
    start_vals = []
    end_vals = []
    for chunk in pd.read_csv(matrix_path, sep='\t', usecols=usecols, chunksize=chunksize):
        if set(coords).issubset(chunk.columns):
            chr_vals.append(chunk[coords[0]].astype(str).to_numpy())
            start_vals.append(chunk[coords[1]].to_numpy())
            end_vals.append(chunk[coords[2]].to_numpy())
        med = chunk[keep_names].median(axis=1, skipna=True).astype(float).to_numpy()
        med_vals.append(med)
    y = np.concatenate(med_vals)
    chrs = np.concatenate(chr_vals) if chr_vals else None
    starts = np.concatenate(start_vals) if start_vals else None
    ends = np.concatenate(end_vals) if end_vals else None

    # sort by chr/start to get consistent x-axis
    if chrs is not None and starts is not None:
        def chr_key_arr(arr):
            out = np.empty(arr.shape, dtype=int)
            for i, s in enumerate(arr):
                s = s[3:] if s.startswith('chr') else s
                if s == 'X': out[i] = 23
                else:
                    try: out[i] = int(s)
                    except: out[i] = 99
            return out
        order = np.lexsort((starts, chr_key_arr(chrs)))
        y = y[order]
        chrs = chrs[order]
        starts = starts[order]
        ends = ends[order]
    return y, chrs, starts, ends

# ---------- Mutation track ----------

def load_mutation_table(path, chr_col=None, start_col=None, end_col=None, value_col=None):
    if path.endswith('.csv'):
        df = pd.read_csv(path)
    else:
        df = pd.read_csv(path, sep='\t')
    cols = {c.lower(): c for c in df.columns}
    def find_col(candidates):
        for cand in candidates:
            for k, v in cols.items():
                if k == cand or k.endswith(cand) or cand in k:
                    return v
        return None
    chr_col = chr_col or find_col(['chr', 'chrom', 'chromosome'])
    start_col = start_col or find_col(['start', 'start_pos', 'start_bp'])
    end_col = end_col or find_col(['end', 'end_pos', 'end_bp'])
    if not value_col:
        for cand in ['mutation_rate','mut_rate','mut_per_mb','mut_density','mut_per_1mb','snvs_per_mb','snvs_per_mbp','snv_density','value']:
            vc = find_col([cand])
            if vc and pd.api.types.is_numeric_dtype(df[vc]):
                value_col = vc
                break
        if not value_col:
            for c in df.columns:
                if c not in [chr_col, start_col, end_col] and pd.api.types.is_numeric_dtype(df[c]):
                    value_col = c; break
    if not all([chr_col, start_col, end_col, value_col]):
        raise ValueError('Could not infer mutation columns')
    df = df[[chr_col, start_col, end_col, value_col]].rename(columns={chr_col:'chr', start_col:'start', end_col:'end', value_col:'value'})
    return df


def sort_by_genome(df):
    def chr_key(series):
        out = []
        for s in series.astype(str):
            if s.startswith('chr'): s = s[3:]
            if s == 'X': out.append(23)
            else:
                try: out.append(int(s))
                except: out.append(99)
        return np.array(out)
    order = np.lexsort((df['start'].values, chr_key(df['chr'])))
    return df.iloc[order].reset_index(drop=True)


def smooth_series(y, win=31):
    if win <= 1: return y
    s = pd.Series(y)
    sm = s.rolling(window=win, center=True, min_periods=max(1, win//4)).median().to_numpy()
    mask = np.isnan(sm)
    sm[mask] = s.to_numpy()[mask]
    return sm


def winsorize(y, upper_pct=99.0):
    if upper_pct is None: return y
    p = np.nanpercentile(y, upper_pct)
    y = y.copy()
    y[y > p] = p
    return y


def chromosome_boundaries(chrs):
    b_ix = [0]
    labels = []
    prev = chrs[0]
    for i, c in enumerate(chrs):
        if c != prev:
            b_ix.append(i)
            labels.append(prev)
            prev = c
    b_ix.append(len(chrs))
    labels.append(prev)
    # Midpoints for labeling
    mids = [ (b_ix[i]+b_ix[i+1])//2 for i in range(len(b_ix)-1) ]
    # Clean labels (strip 'chr') and compact (e.g., every other)
    lab = [str(l)[3:] if str(l).startswith('chr') else str(l) for l in labels]
    return b_ix, mids, lab

# ---------- Combined plotting ----------

def plot_combined(y_norm, y_canc, chrs_for_ticks, mut_series, out_png, title_top='CA median across genome windows', title_bottom='Mutation rate across genome windows', smooth_ca=31, smooth_mut=31, winsor_pct=99.0, mask_zeros=True, add_chrom_bars=True, dpi=600, out_ca_png=None, out_mut_png=None, save_pdf=False):
    # Prepare mutation series (already aligned to CA window order)
    y_mut = mut_series.astype(float)
    if mask_zeros:
        y_mut = y_mut.copy()
        y_mut[y_mut == 0] = np.nan
    y_mut = winsorize(y_mut, upper_pct=winsor_pct)
    if smooth_mut and smooth_mut > 1:
        y_mut = smooth_series(y_mut, win=smooth_mut)

    # Smooth CA if requested
    if smooth_ca and smooth_ca > 1:
        y_norm = smooth_series(y_norm, win=smooth_ca)
        y_canc = smooth_series(y_canc, win=smooth_ca)

    # X axis
    x = np.arange(1, len(y_canc)+1)

    # Setup figure
    fig, axes = plt.subplots(2, 1, sharex=True, figsize=(12, 6), gridspec_kw={'hspace':0.15})

    # Top: CA
    ax = axes[0]
    ax.plot(x, y_norm, color='#1f77b4', alpha=0.95, linewidth=1.2, label='Normal CA (median)')
    ax.plot(x, y_canc, color='#d62728', alpha=0.95, linewidth=1.2, label='Cancer CA (median)')
    ax.set_ylabel('CA signal')
    ax.set_title(title_top)
    ax.legend(loc='upper center', ncol=2,
              frameon=True, framealpha=0.85, facecolor='white', edgecolor='none')
    sns.despine(ax=ax)

    # Bottom: Mutation
    ax2 = axes[1]
    ax2.plot(x, y_mut, color='#8c6bb1', linewidth=1.2, alpha=0.95)
    ax2.set_xlabel('Genome window index (sorted by chr, start)')
    ax2.set_ylabel('Mutation rate')
    ax2.set_title(title_bottom)
    sns.despine(ax=ax2)

    # Chromosome boundaries and labels
    if add_chrom_bars:
        chrs = chrs_for_ticks.astype(str)
        b_ix, mids, labs = chromosome_boundaries(chrs)
        for bx in b_ix[1:-1]:
            axes[0].axvline(bx, color='lightgray', linewidth=0.6, alpha=0.6)
            axes[1].axvline(bx, color='lightgray', linewidth=0.6, alpha=0.6)
        # Label every other chromosome to avoid clutter
        tick_ix = [mids[i] for i in range(len(mids)) if i % 2 == 0]
        tick_labs = [labs[i] for i in range(len(labs)) if i % 2 == 0]
        axes[1].set_xticks(tick_ix)
        axes[1].set_xticklabels(tick_labs, fontsize=9)

    plt.tight_layout()
    os.makedirs(os.path.dirname(out_png), exist_ok=True)
    # Save combined (PNG + optional PDF)
    plt.savefig(out_png, dpi=dpi)
    if save_pdf and out_png.lower().endswith('.png'):
        plt.savefig(out_png[:-4] + '.pdf')
    plt.close()

    # Optional: separate panels
    if out_ca_png:
        fig_ca, axc = plt.subplots(1, 1, figsize=(12, 3))
        axc.plot(x, y_norm, color='#1f77b4', alpha=0.95, linewidth=1.2, label='Normal CA (median)')
        axc.plot(x, y_canc, color='#d62728', alpha=0.95, linewidth=1.2, label='Cancer CA (median)')
        axc.set_ylabel('CA signal')
        axc.set_title(title_top)
        axc.legend(loc='upper center', ncol=2, frameon=True, framealpha=0.85, facecolor='white', edgecolor='none')
        sns.despine(ax=axc)
        os.makedirs(os.path.dirname(out_ca_png), exist_ok=True)
        fig_ca.tight_layout()
        fig_ca.savefig(out_ca_png, dpi=dpi)
        if save_pdf and out_ca_png.lower().endswith('.png'):
            fig_ca.savefig(out_ca_png[:-4] + '.pdf')
        plt.close(fig_ca)

    if out_mut_png:
        fig_m, axm = plt.subplots(1, 1, figsize=(12, 3))
        axm.plot(x, y_mut, color='#8c6bb1', linewidth=1.2, alpha=0.95)
        axm.set_xlabel('Genome window index (sorted by chr, start)')
        axm.set_ylabel('Mutation rate')
        axm.set_title(title_bottom)
        sns.despine(ax=axm)
        os.makedirs(os.path.dirname(out_mut_png), exist_ok=True)
        fig_m.tight_layout()
        fig_m.savefig(out_mut_png, dpi=dpi)
        if save_pdf and out_mut_png.lower().endswith('.png'):
            fig_m.savefig(out_mut_png[:-4] + '.pdf')
        plt.close(fig_m)

# ---------- Main ----------

def main():
    ap = argparse.ArgumentParser(description='Create a 2-panel figure: CA (median normal/cancer) + mutation track with shared genome window x-axis.')
    ap.add_argument('--annotations', required=True)
    ap.add_argument('--cancer-matrix-1mb', required=True)
    ap.add_argument('--normal-matrix-1mb', required=True)
    ap.add_argument('--mutation-file', required=True)
    ap.add_argument('--out-png', default='04_figures/CA_Mutation_combined_1mb.png')
    ap.add_argument('--out-ca-png')
    ap.add_argument('--out-mut-png')
    ap.add_argument('--save-pdf', action='store_true')
    ap.add_argument('--smooth-ca', type=int, default=31)
    ap.add_argument('--smooth-mut', type=int, default=31)
    ap.add_argument('--winsor-pct', type=float, default=99.0)
    ap.add_argument('--mask-zeros', action='store_true')
    ap.add_argument('--dpi', type=int, default=600)
    args = ap.parse_args()

    ids = load_ids(args.annotations)
    y_canc, chrs_c, starts_c, ends_c = median_track(args.cancer_matrix_1mb, include_samples=ids['ca_cancer'], exclude_samples=ids['rt'])
    y_norm, chrs_n, starts_n, ends_n = median_track(args.normal_matrix_1mb, include_samples=ids['ca_normal'], exclude_samples=ids['rt'])

    # Align mutation to CA windows via left join on chr,start,end (CA order preserved)
    mut_df = load_mutation_table(args.mutation_file)
    ca_coords = pd.DataFrame({'chr': chrs_c, 'start': starts_c, 'end': ends_c})
    mut_aligned = pd.merge(ca_coords, mut_df, on=['chr','start','end'], how='left')
    mut_series = mut_aligned['value']

    plot_combined(y_norm, y_canc, ca_coords['chr'].astype(str).to_numpy(), mut_series, args.out_png,
                  smooth_ca=args.smooth_ca, smooth_mut=args.smooth_mut,
                  winsor_pct=args.winsor_pct, mask_zeros=args.mask_zeros,
                  dpi=args.dpi,
                  out_ca_png=args.out_ca_png, out_mut_png=args.out_mut_png,
                  save_pdf=args.save_pdf)
    print('Saved combined figure to:', args.out_png)
    if args.out_ca_png:
        print('Saved CA-only figure to:', args.out_ca_png)
        if args.save_pdf and args.out_ca_png.lower().endswith('.png'):
            print('Saved CA-only PDF to:', args.out_ca_png[:-4] + '.pdf')
    if args.out_mut_png:
        print('Saved Mutation-only figure to:', args.out_mut_png)
        if args.save_pdf and args.out_mut_png.lower().endswith('.png'):
            print('Saved Mutation-only PDF to:', args.out_mut_png[:-4] + '.pdf')

if __name__ == '__main__':
    main()

