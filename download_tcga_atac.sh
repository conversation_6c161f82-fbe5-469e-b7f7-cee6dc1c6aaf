#!/bin/bash
# TCGA ATAC-seq Download Script
# Run this script to download ATAC-seq data for new cancer types

set -e  # Exit on any error

echo "=== TCGA ATAC-seq Download Pipeline ==="
echo "Downloading data for 6 new cancer types"
echo

# Create directories
mkdir -p tcga_downloads/{metadata,atac_seq,processed,logs}

# Function to download data for a cancer type
download_cancer_type() {
    local cancer_type=$1
    local tcga_codes=$2
    
    echo "📥 Downloading $cancer_type ($tcga_codes)..."
    
    # Create cancer-specific directory
    mkdir -p "tcga_downloads/atac_seq/$cancer_type"
    
    # Query GDC API for file list
    python3 -c "
import requests
import json
import pandas as pd

# Query parameters
filters = {
    'op': 'and',
    'content': [
        {
            'op': 'in',
            'content': {
                'field': 'cases.project.project_id',
                'value': ['$tcga_codes']
            }
        },
        {
            'op': 'in',
            'content': {
                'field': 'data_type',
                'value': ['ATAC-seq']
            }
        }
    ]
}

params = {
    'filters': json.dumps(filters),
    'expand': 'cases.project,cases.submitter_id',
    'format': 'json',
    'size': '2000'
}

# Query API
response = requests.get('https://api.gdc.cancer.gov/files', params=params)
data = response.json()

# Save metadata
with open('tcga_downloads/metadata/${cancer_type}_files.json', 'w') as f:
    json.dump(data, f, indent=2)

print(f'Found {len(data["data"])} files for $cancer_type')
"
    
    # Download files using GDC client (if available)
    if command -v gdctools &> /dev/null; then
        echo "Using GDC tools for download..."
        # Add GDC download commands here
    else
        echo "⚠️  GDC tools not found. Please install from: https://gdc.cancer.gov/access-data/gdc-data-transfer-tool"
        echo "Alternative: Use TCGA Data Portal for manual download"
    fi
    
    echo "✅ Completed $cancer_type"
    echo
}

# Download each cancer type
echo "Starting downloads..."

download_cancer_type "skin" "TCGA-SKCM"
download_cancer_type "esophagus" "TCGA-ESCA"  
download_cancer_type "liver" "TCGA-LIHC"
download_cancer_type "stomach" "TCGA-STAD"
download_cancer_type "head_neck" "TCGA-HNSC,TCGA-THCA"
download_cancer_type "uterus" "TCGA-UCEC,TCGA-CESC"

echo "🎉 Download pipeline completed!"
echo "Check tcga_downloads/ directory for results"
