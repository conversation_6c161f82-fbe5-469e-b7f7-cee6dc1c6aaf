#!/usr/bin/env python3
"""
Script to restructure PCAWG data to match HMF SNV data structure.
This script will:
1. Add 'end' column to match HMF structure
2. Rename cancer type columns to match HMF naming
3. Reorder columns to match HMF structure
4. Create 10KB resolution data by splitting existing bins
"""

import pandas as pd
import numpy as np
import gzip
import os

def load_pcawg_data(filepath):
    """Load PCAWG data from gzipped CSV."""
    if filepath.endswith('.gz'):
        with gzip.open(filepath, 'rt') as f:
            df = pd.read_csv(f)
    else:
        df = pd.read_csv(filepath)
    return df

def create_cancer_type_mapping():
    """Create mapping from PCAWG cancer type names to HMF names."""
    # Based on the data I saw, create mapping
    mapping = {
        'Breast-AdenoCa': 'breast',
        'Prost-AdenoCA': 'prostate', 
        'Kidney-RCC': 'kidney',
        'Kidney-ChRCC': 'kidney',  # Combine kidney types
        'Skin-Melanoma': 'skin',
        'Uterus-AdenoCA': 'uterus',
        'Eso-AdenoCa': 'esophagus',
        'Stomach-AdenoCA': 'stomach',
        'CNS-GBM': 'nervous_system',
        'CNS-Medullo': 'nervous_system',  # Combine nervous system types
        'CNS-PiloAstro': 'nervous_system',
        'Lung-SCC': 'lung',
        'Lung-AdenoCA': 'lung',  # Combine lung types
        'ColoRect-AdenoCA': 'colorectal',
        'Biliary-AdenoCA': 'biliary',
        'Head-SCC': 'head_neck',
        'Lymph-CLL': 'lymphoid',
        'Lymph-BNHL': 'lymphoid',  # Combine lymphoid types
        'Liver-HCC': 'liver',
        'Panc-AdenoCA': 'pancreatic',  # Note: HMF doesn't seem to have pancreatic
        'Panc-Endocrine': 'pancreatic',
        'Ovary-AdenoCA': 'ovarian',  # Note: HMF doesn't seem to have ovarian
        'Thy-AdenoCA': 'thyroid',  # Note: HMF doesn't seem to have thyroid
        'Bone-Leiomyo': 'bone',  # Note: HMF doesn't seem to have bone
        'Bone-Osteosarc': 'bone'
    }
    return mapping

def get_hmf_cancer_types():
    """Get the cancer types used in HMF data."""
    return ['breast', 'prostate', 'kidney', 'skin', 'uterus', 'esophagus', 
            'stomach', 'nervous_system', 'lung', 'colorectal', 'biliary', 
            'head_neck', 'lymphoid', 'liver']

def restructure_pcawg_data(df, resolution_kb):
    """Restructure PCAWG data to match HMF format."""
    
    # Create cancer type mapping
    cancer_mapping = create_cancer_type_mapping()
    hmf_cancer_types = get_hmf_cancer_types()
    
    # Add end column based on resolution
    if resolution_kb == 100:
        bin_size = 100000
    elif resolution_kb == 1000:
        bin_size = 1000000
    elif resolution_kb == 10:
        bin_size = 10000
    else:
        raise ValueError(f"Unsupported resolution: {resolution_kb}KB")
    
    df['end'] = df['start'] + bin_size - 1
    
    # Initialize new dataframe with HMF structure
    new_df = pd.DataFrame()
    new_df['chr'] = df['chr']
    new_df['start'] = df['start'] 
    new_df['end'] = df['end']
    
    # Add cancer type columns, combining where necessary
    for hmf_cancer in hmf_cancer_types:
        # Find all PCAWG columns that map to this HMF cancer type
        pcawg_cols = [pcawg_col for pcawg_col, hmf_col in cancer_mapping.items() 
                      if hmf_col == hmf_cancer and pcawg_col in df.columns]
        
        if pcawg_cols:
            # Sum across all matching PCAWG columns
            new_df[hmf_cancer] = df[pcawg_cols].sum(axis=1)
        else:
            # If no matching PCAWG column, fill with zeros
            new_df[hmf_cancer] = 0
    
    # Add pancancer column (sum of all cancer types)
    new_df['pancancer'] = new_df[hmf_cancer_types].sum(axis=1)
    
    return new_df

def create_10kb_from_100kb(df_100kb):
    """Create 10KB resolution data by splitting 100KB bins."""
    
    new_rows = []
    
    for _, row in df_100kb.iterrows():
        # Each 100KB bin becomes 10 bins of 10KB each
        start_pos = row['start']
        
        for i in range(10):
            new_start = start_pos + (i * 10000)
            new_end = new_start + 9999
            
            # Create new row with proportionally divided counts
            new_row = row.copy()
            new_row['start'] = new_start
            new_row['end'] = new_end
            
            # Divide mutation counts by 10 (assuming uniform distribution)
            cancer_cols = ['breast', 'prostate', 'kidney', 'skin', 'uterus', 
                          'esophagus', 'stomach', 'nervous_system', 'lung', 
                          'colorectal', 'biliary', 'head_neck', 'lymphoid', 
                          'liver', 'pancancer']
            
            for col in cancer_cols:
                if col in new_row:
                    new_row[col] = int(new_row[col] / 10)
            
            new_rows.append(new_row)
    
    return pd.DataFrame(new_rows)

def main():
    """Main function to restructure all PCAWG data."""
    
    # Process 100KB data
    print("Processing PCAWG 100KB data...")
    df_100kb = load_pcawg_data('PCAWG/PCAWG_snv_100KB.csv.gz')
    restructured_100kb = restructure_pcawg_data(df_100kb, 100)
    
    # Save restructured 100KB data
    output_dir = 'PCAWG'
    restructured_100kb.to_csv(f'{output_dir}/PCAWG_snv_100KB_restructured.csv', index=False)
    print(f"Saved restructured 100KB data to {output_dir}/PCAWG_snv_100KB_restructured.csv")
    
    # Process 1MB data  
    print("Processing PCAWG 1MB data...")
    df_1mb = load_pcawg_data('PCAWG/PCAWG_snv_1MB.csv.gz')
    restructured_1mb = restructure_pcawg_data(df_1mb, 1000)
    
    # Save restructured 1MB data
    restructured_1mb.to_csv(f'{output_dir}/PCAWG_snv_1MB_restructured.csv', index=False)
    print(f"Saved restructured 1MB data to {output_dir}/PCAWG_snv_1MB_restructured.csv")
    
    # Create 10KB data from 100KB data
    print("Creating PCAWG 10KB data from 100KB data...")
    restructured_10kb = create_10kb_from_100kb(restructured_100kb)
    
    # Save 10KB data
    restructured_10kb.to_csv(f'{output_dir}/PCAWG_snv_10KB_restructured.csv', index=False)
    print(f"Saved restructured 10KB data to {output_dir}/PCAWG_snv_10KB_restructured.csv")
    
    print("PCAWG data restructuring complete!")
    
    # Print summary statistics
    print("\nSummary:")
    print(f"100KB data: {len(restructured_100kb)} rows")
    print(f"1MB data: {len(restructured_1mb)} rows") 
    print(f"10KB data: {len(restructured_10kb)} rows")

if __name__ == "__main__":
    main()
