#!/usr/bin/env python3
"""
Create a 3-panel figure for new_ca_rt_mutation data:
- Top: CA_RT signal (median across cancer types)
- Middle: SNV mutations (pancancer)
- Bottom: INDEL mutations (pancancer)
All using 1MB resolution data with shared genome window x-axis.
"""

import os
import argparse
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

sns.set_theme(style="white", context="paper", font_scale=1.2)

def load_ca_rt_data(ca_rt_path):
    """Load CA_RT data and compute median across cancer samples"""
    print(f"📥 Loading CA_RT data: {ca_rt_path}")
    df = pd.read_csv(ca_rt_path, sep='\t')

    # Get coordinate columns
    coord_cols = ['chr', 'start', 'end']

    # Get sample columns (exclude coordinates)
    sample_cols = [col for col in df.columns if col not in coord_cols]

    # Filter for cancer samples (exclude normal samples)
    cancer_cols = [col for col in sample_cols if 'Cancer' in col or 'cancer' in col or 'tumor' in col or 'Tumor' in col]

    if len(cancer_cols) == 0:
        # If no clear cancer samples, use all samples
        cancer_cols = sample_cols
        print(f"   No clear cancer samples found, using all {len(cancer_cols)} samples")
    else:
        print(f"   Found {len(cancer_cols)} cancer samples out of {len(sample_cols)} total samples")

    print(f"   Rows: {len(df):,}")

    # Compute median CA_RT across cancer samples
    ca_median = df[cancer_cols].median(axis=1, skipna=True).astype(float)

    return df[coord_cols].copy(), ca_median, cancer_cols

def load_mutation_data(mut_path, mut_type, cancer_type='pancancer'):
    """Load mutation data (SNV or INDEL)"""
    print(f"📥 Loading {mut_type} data: {mut_path}")
    df = pd.read_csv(mut_path)

    # Get coordinate columns
    coord_cols = ['chr', 'start', 'end']
    cancer_cols = [col for col in df.columns if col not in coord_cols]

    # Select mutation signal based on cancer type
    if cancer_type == 'pancancer':
        if 'pancancer' in df.columns:
            mut_signal = df['pancancer'].astype(float)
        else:
            # Fallback: sum all non-coordinate columns
            mut_signal = df[cancer_cols].sum(axis=1).astype(float)
    elif cancer_type in df.columns:
        mut_signal = df[cancer_type].astype(float)
    else:
        raise ValueError(f"Cancer type '{cancer_type}' not found in {mut_type} data. Available: {cancer_cols}")

    print(f"   Rows: {len(df):,}")
    print(f"   Cancer type: {cancer_type}")
    print(f"   Total {mut_type} mutations: {mut_signal.sum():,}")

    return df[coord_cols].copy(), mut_signal, cancer_cols

def sort_by_genome(df):
    """Sort dataframe by chromosome and start position"""
    def chr_key(series):
        out = []
        for s in series.astype(str):
            if s.startswith('chr'): 
                s = s[3:]
            if s == 'X': 
                out.append(23)
            elif s == 'Y':
                out.append(24)
            else:
                try: 
                    out.append(int(s))
                except: 
                    out.append(99)
        return np.array(out)
    
    order = np.lexsort((df['start'].values, chr_key(df['chr'])))
    return df.iloc[order].reset_index(drop=True)

def smooth_series(y, win=31):
    """Apply rolling median smoothing"""
    if win <= 1: 
        return y
    s = pd.Series(y)
    sm = s.rolling(window=win, center=True, min_periods=max(1, win//4)).median().to_numpy()
    mask = np.isnan(sm)
    sm[mask] = s.to_numpy()[mask]
    return sm

def winsorize(y, upper_pct=99.0):
    """Cap extreme values at specified percentile"""
    if upper_pct is None: 
        return y
    p = np.nanpercentile(y, upper_pct)
    y = y.copy()
    y[y > p] = p
    return y

def chromosome_boundaries(chrs):
    """Find chromosome boundaries for plotting vertical lines"""
    b_ix = [0]
    labels = []
    prev = chrs[0]
    for i, c in enumerate(chrs):
        if c != prev:
            b_ix.append(i)
            labels.append(prev)
            prev = c
    b_ix.append(len(chrs))
    labels.append(prev)
    
    # Midpoints for labeling
    mids = [(b_ix[i] + b_ix[i+1]) // 2 for i in range(len(b_ix)-1)]
    
    # Clean labels (strip 'chr')
    lab = [str(l)[3:] if str(l).startswith('chr') else str(l) for l in labels]
    return b_ix, mids, lab

def plot_ca_rt_mutations(ca_coords, ca_signal, snv_signal, indel_signal, 
                        out_png, smooth_ca=31, smooth_mut=31, winsor_pct=99.0, 
                        mask_zeros=True, add_chrom_bars=True, dpi=600, save_pdf=False):
    """Create 3-panel plot: CA_RT + SNV + INDEL"""
    
    # Sort all data by genomic coordinates
    combined_df = ca_coords.copy()
    combined_df['ca_signal'] = ca_signal
    combined_df['snv_signal'] = snv_signal  
    combined_df['indel_signal'] = indel_signal
    combined_df = sort_by_genome(combined_df)
    
    # Extract sorted signals
    y_ca = combined_df['ca_signal'].to_numpy()
    y_snv = combined_df['snv_signal'].to_numpy()
    y_indel = combined_df['indel_signal'].to_numpy()
    chrs = combined_df['chr'].to_numpy()
    
    # Process mutation signals
    if mask_zeros:
        y_snv = y_snv.copy()
        y_indel = y_indel.copy()
        y_snv[y_snv == 0] = np.nan
        y_indel[y_indel == 0] = np.nan
    
    # Winsorize extreme values
    y_snv = winsorize(y_snv, upper_pct=winsor_pct)
    y_indel = winsorize(y_indel, upper_pct=winsor_pct)
    
    # Apply smoothing
    if smooth_ca and smooth_ca > 1:
        y_ca = smooth_series(y_ca, win=smooth_ca)
    if smooth_mut and smooth_mut > 1:
        y_snv = smooth_series(y_snv, win=smooth_mut)
        y_indel = smooth_series(y_indel, win=smooth_mut)
    
    # X axis
    x = np.arange(1, len(y_ca) + 1)
    
    # Setup figure with 3 panels
    fig, axes = plt.subplots(3, 1, sharex=True, figsize=(14, 9), 
                            gridspec_kw={'hspace': 0.2, 'height_ratios': [1, 1, 1]})
    
    # Top panel: CA_RT
    ax1 = axes[0]
    ax1.plot(x, y_ca, color='#2E8B57', alpha=0.95, linewidth=1.2, label='CA_RT (median)')
    ax1.set_ylabel('CA_RT Signal')
    ax1.set_title('Chromatin Accessibility (CA_RT) across 1MB genome windows')
    ax1.legend(loc='upper right', frameon=True, framealpha=0.85, facecolor='white', edgecolor='none')
    sns.despine(ax=ax1)
    
def plot_ca_rt_mutations(ca_coords, ca_signal, snv_signal, indel_signal,
                        out_png, cancer_type='pancancer', smooth_ca=31, smooth_mut=31, winsor_pct=99.0,
                        mask_zeros=True, add_chrom_bars=True, dpi=600, save_pdf=False):
    """Create 2-panel plot: CA + separate SNV and INDEL lines"""

    # Sort all data by genomic coordinates
    combined_df = ca_coords.copy()
    combined_df['ca_signal'] = ca_signal
    combined_df['snv_signal'] = snv_signal
    combined_df['indel_signal'] = indel_signal
    combined_df = sort_by_genome(combined_df)

    # Extract sorted signals
    y_ca = combined_df['ca_signal'].to_numpy()
    y_snv = combined_df['snv_signal'].to_numpy()
    y_indel = combined_df['indel_signal'].to_numpy()
    chrs = combined_df['chr'].to_numpy()

    # Process mutation signals
    if mask_zeros:
        y_snv = y_snv.copy()
        y_indel = y_indel.copy()
        y_snv[y_snv == 0] = np.nan
        y_indel[y_indel == 0] = np.nan

    # Winsorize extreme values
    y_snv = winsorize(y_snv, upper_pct=winsor_pct)
    y_indel = winsorize(y_indel, upper_pct=winsor_pct)

    # Apply smoothing
    if smooth_ca and smooth_ca > 1:
        y_ca = smooth_series(y_ca, win=smooth_ca)
    if smooth_mut and smooth_mut > 1:
        y_snv = smooth_series(y_snv, win=smooth_mut)
        y_indel = smooth_series(y_indel, win=smooth_mut)

    # X axis
    x = np.arange(1, len(y_ca) + 1)

    # Setup figure with 2 panels
    fig, axes = plt.subplots(2, 1, sharex=True, figsize=(14, 8),
                            gridspec_kw={'hspace': 0.25, 'height_ratios': [1, 1]})

    # Top panel: CA
    ax1 = axes[0]
    ax1.plot(x, y_ca, color='#2E8B57', alpha=0.95, linewidth=1.2, label='CA (median)')
    ax1.set_ylabel('CA Signal')
    ax1.set_title('Chromatin Accessibility (CA) across 1MB genome windows')
    ax1.legend(loc='upper right', frameon=True, framealpha=0.85, facecolor='white', edgecolor='none')
    sns.despine(ax=ax1)

    # Bottom panel: Separate SNV and INDEL lines
    ax2 = axes[1]
    snv_label = f'SNV ({cancer_type})'
    indel_label = f'INDEL ({cancer_type})'
    ax2.plot(x, y_snv, color='#1f77b4', linewidth=1.2, alpha=0.95, label=snv_label)
    ax2.plot(x, y_indel, color='#d62728', linewidth=1.2, alpha=0.95, label=indel_label)
    ax2.set_xlabel('Genome window index (sorted by chr, start)')
    ax2.set_ylabel('Mutation Count')
    ax2.set_title(f'Mutations - {cancer_type} across 1MB genome windows')
    ax2.legend(loc='upper right', frameon=True, framealpha=0.85, facecolor='white', edgecolor='none')
    sns.despine(ax=ax2)
    
    # Add chromosome boundaries and labels
    if add_chrom_bars:
        chrs_str = chrs.astype(str)
        b_ix, mids, labs = chromosome_boundaries(chrs_str)

        # Add vertical lines at chromosome boundaries
        for bx in b_ix[1:-1]:
            for ax in axes:
                ax.axvline(bx, color='lightgray', linewidth=0.6, alpha=0.6)

        # Label every other chromosome to avoid clutter
        tick_ix = [mids[i] for i in range(len(mids)) if i % 2 == 0]
        tick_labs = [labs[i] for i in range(len(labs)) if i % 2 == 0]
        ax2.set_xticks(tick_ix)
        ax2.set_xticklabels(tick_labs, fontsize=9)
    
    plt.tight_layout()
    
    # Save figure
    os.makedirs(os.path.dirname(out_png), exist_ok=True)
    plt.savefig(out_png, dpi=dpi, bbox_inches='tight')
    if save_pdf and out_png.lower().endswith('.png'):
        plt.savefig(out_png[:-4] + '.pdf', bbox_inches='tight')
    plt.close()
    
    print(f"✅ Saved combined figure: {out_png}")
    if save_pdf:
        print(f"✅ Saved PDF: {out_png[:-4] + '.pdf'}")

def main():
    parser = argparse.ArgumentParser(description='Create 3-panel CA_RT + SNV + INDEL figure from new_ca_rt_mutation 1MB data')
    parser.add_argument('--ca-rt-1mb', default='new_ca_rt_mutation/CA_RT_1MB.tsv',
                       help='CA_RT 1MB data file')
    parser.add_argument('--snv-1mb', default='new_ca_rt_mutation/HMF_snv_1MB.csv',
                       help='SNV 1MB data file')
    parser.add_argument('--indel-1mb', default='new_ca_rt_mutation/HMF_indel_1MB.csv',
                       help='INDEL 1MB data file')
    parser.add_argument('--out-png', default='figures/CA_mutations_1MB_combined.png',
                       help='Output PNG file')
    parser.add_argument('--smooth-ca', type=int, default=31,
                       help='Smoothing window for CA_RT (default: 31)')
    parser.add_argument('--smooth-mut', type=int, default=31,
                       help='Smoothing window for mutations (default: 31)')
    parser.add_argument('--winsor-pct', type=float, default=99.0,
                       help='Winsorization percentile (default: 99.0)')
    parser.add_argument('--mask-zeros', action='store_true',
                       help='Mask zero mutation values as NaN')
    parser.add_argument('--dpi', type=int, default=600,
                       help='Figure DPI (default: 600)')
    parser.add_argument('--save-pdf', action='store_true',
                       help='Also save PDF version')
    parser.add_argument('--cancer-type', default='pancancer',
                       help='Cancer type to plot for mutations (default: pancancer)')
    parser.add_argument('--list-cancer-types', action='store_true',
                       help='List available cancer types and exit')
    
    args = parser.parse_args()

    # Handle list cancer types option
    if args.list_cancer_types:
        print("📋 Available cancer types:")
        df_snv = pd.read_csv(args.snv_1mb)
        cancer_cols = [col for col in df_snv.columns if col not in ['chr', 'start', 'end']]
        for i, ctype in enumerate(cancer_cols, 1):
            print(f"  {i:2d}. {ctype}")
        return

    print("🔄 Creating CA + combined mutations figure\n")

    # Load data
    ca_coords, ca_signal, ca_samples = load_ca_rt_data(args.ca_rt_1mb)
    snv_coords, snv_signal, snv_cancer_types = load_mutation_data(args.snv_1mb, 'SNV', args.cancer_type)
    indel_coords, indel_signal, indel_cancer_types = load_mutation_data(args.indel_1mb, 'INDEL', args.cancer_type)
    
    # Verify coordinate alignment
    print(f"\n🔍 Verifying coordinate alignment:")
    print(f"   CA_RT rows: {len(ca_coords):,}")
    print(f"   SNV rows: {len(snv_coords):,}")
    print(f"   INDEL rows: {len(indel_coords):,}")
    
    # Check if coordinates match
    ca_key = ca_coords['chr'].astype(str) + ':' + ca_coords['start'].astype(str) + '-' + ca_coords['end'].astype(str)
    snv_key = snv_coords['chr'].astype(str) + ':' + snv_coords['start'].astype(str) + '-' + snv_coords['end'].astype(str)
    indel_key = indel_coords['chr'].astype(str) + ':' + indel_coords['start'].astype(str) + '-' + indel_coords['end'].astype(str)
    
    if not ca_key.equals(snv_key) or not ca_key.equals(indel_key):
        print("   ⚠️  Coordinates don't match exactly - will align by merge")
        # Merge all data on coordinates
        merged = ca_coords.copy()
        merged['ca_signal'] = ca_signal
        merged = merged.merge(snv_coords.assign(snv_signal=snv_signal), on=['chr', 'start', 'end'], how='inner')
        merged = merged.merge(indel_coords.assign(indel_signal=indel_signal), on=['chr', 'start', 'end'], how='inner')
        
        ca_coords = merged[['chr', 'start', 'end']].copy()
        ca_signal = merged['ca_signal']
        snv_signal = merged['snv_signal']
        indel_signal = merged['indel_signal']
        print(f"   ✅ Aligned to {len(merged):,} common windows")
    else:
        print("   ✅ Coordinates match perfectly")
    
    # Create plot
    plot_ca_rt_mutations(ca_coords, ca_signal, snv_signal, indel_signal,
                        args.out_png, cancer_type=args.cancer_type, smooth_ca=args.smooth_ca, smooth_mut=args.smooth_mut,
                        winsor_pct=args.winsor_pct, mask_zeros=args.mask_zeros,
                        dpi=args.dpi, save_pdf=args.save_pdf)
    
    print(f"\n🎉 Complete! Figure saved to: {args.out_png}")

if __name__ == '__main__':
    main()
