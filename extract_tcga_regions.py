#!/usr/bin/env python3
"""
Extract ATAC-seq signals for specific genomic regions
Processes downloaded TCGA ATAC-seq data to match your existing regions
"""

import pandas as pd
import numpy as np
import pyBigWig
import os
from pathlib import Path

def load_genomic_regions():
    """Load your existing genomic regions (10KB windows)"""
    
    # Load from your existing CA_RT file to get coordinates
    ca_rt_file = 'new_ca_rt_mutation/CA_RT_10KB.tsv'
    
    if os.path.exists(ca_rt_file):
        df = pd.read_csv(ca_rt_file, sep='\t')
        regions = df[['chr', 'start', 'end']].copy()
        print(f"✅ Loaded {len(regions):,} genomic regions")
        return regions
    else:
        print("❌ CA_RT file not found. Creating default regions...")
        # Create default 10KB regions if file not found
        regions = []
        for chrom in range(1, 23):  # chr1-22
            # Example: create regions every 10KB (adjust as needed)
            for start in range(1, 250000000, 10000):  # Approximate chromosome length
                end = start + 9999
                regions.append({'chr': f'chr{chrom}', 'start': start, 'end': end})
        
        return pd.DataFrame(regions)

def extract_atac_signals(bigwig_file, regions_df):
    """Extract ATAC-seq signals for genomic regions"""
    
    print(f"Processing {bigwig_file}...")
    
    try:
        bw = pyBigWig.open(bigwig_file)
        signals = []
        
        for idx, row in regions_df.iterrows():
            chrom = row['chr']
            start = int(row['start'])
            end = int(row['end'])
            
            try:
                # Get mean signal in region
                signal = bw.stats(chrom, start, end, type="mean")[0]
                if signal is None:
                    signal = 0.0
                signals.append(signal)
            except:
                signals.append(0.0)
            
            if (idx + 1) % 10000 == 0:
                print(f"    Processed {idx + 1:,} regions...")
        
        bw.close()
        return signals
        
    except Exception as e:
        print(f"❌ Error processing {bigwig_file}: {e}")
        return [0.0] * len(regions_df)

def process_cancer_type(cancer_type):
    """Process all ATAC-seq files for a cancer type"""
    
    print(f"\n=== Processing {cancer_type.upper()} ===")
    
    # Directory containing downloaded files
    data_dir = f'tcga_downloads/atac_seq/{cancer_type}'
    
    if not os.path.exists(data_dir):
        print(f"❌ Directory not found: {data_dir}")
        return
    
    # Find BigWig files
    bigwig_files = list(Path(data_dir).glob('**/*.bw')) + list(Path(data_dir).glob('**/*.bigwig'))
    
    if not bigwig_files:
        print(f"❌ No BigWig files found in {data_dir}")
        return
    
    print(f"Found {len(bigwig_files)} BigWig files")
    
    # Load genomic regions
    regions_df = load_genomic_regions()
    
    # Process each file
    results = {}
    for bigwig_file in bigwig_files:
        sample_name = bigwig_file.stem
        signals = extract_atac_signals(str(bigwig_file), regions_df)
        results[sample_name] = signals
    
    # Create output DataFrame
    output_df = regions_df.copy()
    for sample_name, signals in results.items():
        output_df[sample_name] = signals
    
    # Save results
    output_file = f'tcga_downloads/processed/{cancer_type}_atac_10KB.tsv'
    output_df.to_csv(output_file, sep='\t', index=False)
    
    print(f"✅ Saved {output_file}")
    print(f"   Regions: {len(output_df):,}")
    print(f"   Samples: {len(results)}")

def main():
    """Main processing function"""
    
    print("=== TCGA ATAC-seq Region Extraction ===\n")
    
    # Cancer types to process
    cancer_types = ['skin', 'esophagus', 'liver', 'stomach', 'head_neck', 'uterus']
    
    for cancer_type in cancer_types:
        process_cancer_type(cancer_type)
    
    print("\n🎉 Processing completed!")
    print("Check tcga_downloads/processed/ for results")

if __name__ == "__main__":
    main()
