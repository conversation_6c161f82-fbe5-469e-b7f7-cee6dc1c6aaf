# 02_run_plots.R
source("01_plot_ca_rt_mut_1mb_pub.R")

# outputs go to ./plots/ (created if missing)
plot_ca_rt_mut_same_x(
  ca_rt_path = "new_ca_rt_mutation/CA_RT_1MB.tsv",
  snv_path   = "new_ca_rt_mutation/HMF_snv_1MB.csv",
  indel_path = "new_ca_rt_mutation/HMF_indel_1MB.csv",
  out_prefix = "plots/CA_RT_SNV_INDEL_1MB_sharedX",
  autosomes_only = TRUE,   # set FALSE if you also want X/Y
  label_every = 1,         # label all chromosomes 1..22
  smooth_ca = 31, smooth_rt = 31, smooth_mut = 31,
  winsor_pct = 99, mut_transform = "log1p"
)
