#!/usr/bin/env python3
"""
Integrate new TCGA ATAC-seq data with existing CA_RT pipeline
"""

import pandas as pd
import os

def integrate_new_cancer_types():
    """Integrate new TCGA cancer types with existing data"""
    
    print("=== INTEGRATING NEW TCGA CANCER TYPES ===\n")
    
    # Load existing CA_RT data
    existing_file = 'new_ca_rt_mutation/CA_RT_10KB.tsv'
    
    if os.path.exists(existing_file):
        df_existing = pd.read_csv(existing_file, sep='\t')
        print(f"✅ Loaded existing CA_RT data: {len(df_existing):,} regions")
    else:
        print("❌ Existing CA_RT file not found")
        return
    
    # Process each new cancer type
    new_cancer_types = ['skin', 'esophagus', 'liver', 'stomach', 'head_neck', 'uterus']
    
    for cancer_type in new_cancer_types:
        tcga_file = f'tcga_downloads/processed/{cancer_type}_atac_10KB.tsv'
        
        if os.path.exists(tcga_file):
            print(f"📥 Processing {cancer_type}...")
            
            df_tcga = pd.read_csv(tcga_file, sep='\t')
            
            # Merge on genomic coordinates
            df_merged = pd.merge(
                df_existing[['chr', 'start', 'end']], 
                df_tcga, 
                on=['chr', 'start', 'end'], 
                how='left'
            )
            
            # Add to existing data
            sample_cols = [col for col in df_tcga.columns if col not in ['chr', 'start', 'end']]
            for col in sample_cols:
                df_existing[col] = df_merged[col]
            
            print(f"   Added {len(sample_cols)} samples")
        else:
            print(f"⚠️  {tcga_file} not found - skipping {cancer_type}")
    
    # Save integrated data
    output_file = 'new_ca_rt_mutation/CA_RT_10KB_with_new_cancers.tsv'
    df_existing.to_csv(output_file, sep='\t', index=False)
    
    print(f"\n✅ Saved integrated data: {output_file}")
    print(f"   Total regions: {len(df_existing):,}")
    print(f"   Total samples: {len(df_existing.columns) - 3}")  # Exclude chr, start, end

def main():
    integrate_new_cancer_types()

if __name__ == "__main__":
    main()
