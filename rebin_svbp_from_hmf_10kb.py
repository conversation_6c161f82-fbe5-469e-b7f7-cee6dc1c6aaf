#!/usr/bin/env python3
"""
Rebuild new_ca_rt_mutation/HMF_svbp_10KB.csv from new_HMF/svbp.10000.by_cancer.tsv
- Match rows (coords, order) and columns to HMF_snv_10KB.csv exactly
- 14 cancer types + pancancer, same order as SNV
- Use interval-overlap rebinning (weighted by overlap length) from HMF 10kb grid
- Emit integer counts; round half up after weighting
"""

import os
import math
import numpy as np
import pandas as pd
from typing import Dict, List, Union

SNV_10KB = 'new_ca_rt_mutation/HMF_snv_10KB.csv'
HMF_SVBP_10KB = 'new_HMF/svbp.10000.by_cancer.tsv'
SVBP_OUT = 'new_ca_rt_mutation/HMF_svbp_10KB.csv'
SVBP_BACKUP = 'new_ca_rt_mutation/HMF_svbp_10KB_BACKUP_before_rebin.csv'

# Mapping: standardized -> HMF column(s)
CANCER_MAPPING: Dict[str, Union[List[str], str]] = {
    'breast': 'Breast',
    'prostate': 'Prostate',
    'kidney': 'Kidney',
    'skin': 'Skin',
    'uterus': ['Endometrium/uterus', 'Cervix'],
    'esophagus': 'Esophagus/gastroesophageal junction',
    'stomach': 'Stomach',
    'nervous_system': 'Brain/central nervous system',
    'lung': 'Lung',
    'colorectal': ['Colon', 'Rectum'],
    'biliary': 'Biliary tract',
    'head_neck': [
        'Hypopharynx', 'Larynx', 'Nasal cavity', 'Nasopharynx',
        'Oral cavity/tongue', 'Oropharynx', 'Salivary gland', 'Thyroid gland'
    ],
    'lymphoid': 'Lymphoid',
    'liver': 'Liver',
}


def round_half_up(x: np.ndarray) -> np.ndarray:
    return np.floor(x + 0.5).astype(np.int64)


def parse_window_id(df_hmf: pd.DataFrame) -> pd.DataFrame:
    parts = df_hmf['window_id'].astype(str).str.split(':', n=1, expand=True)
    chrom = parts[0]
    se = parts[1].str.split('-', n=1, expand=True)
    out = pd.DataFrame({
        'chr': 'chr' + chrom.astype(str),
        'start': se[0].astype(np.int64),
        'end': se[1].astype(np.int64),
    })
    return out


def build_hmf_matrix(df_hmf: pd.DataFrame, cancer_cols_order: List[str]) -> pd.DataFrame:
    # Create per-cancer numeric columns (sum lists where needed)
    out = pd.DataFrame(index=df_hmf.index)
    for std in cancer_cols_order:
        src = CANCER_MAPPING.get(std)
        if isinstance(src, list):
            present = [c for c in src if c in df_hmf.columns]
            if present:
                out[std] = df_hmf[present].sum(axis=1).astype(np.int64)
            else:
                out[std] = 0
        else:
            out[std] = df_hmf[src].astype(np.int64) if src in df_hmf.columns else 0
    return out


def rebin_chrom(df_target_chr: pd.DataFrame, df_hmf_chr: pd.DataFrame, vals_chr: np.ndarray) -> np.ndarray:
    """Weighted overlap rebin per chromosome.
    df_target_chr: rows with ['start','end'] (target bins)
    df_hmf_chr: rows with ['start','end'] (hmf bins, 10kb grid from 0)
    vals_chr: shape (n_hmf_bins, n_cancers)
    Returns: array shape (n_target_bins, n_cancers)
    """
    t_starts = df_target_chr['start'].to_numpy(np.int64)
    t_ends = df_target_chr['end'].to_numpy(np.int64)
    h_starts = df_hmf_chr['start'].to_numpy(np.int64)
    h_ends = df_hmf_chr['end'].to_numpy(np.int64)

    n_t = len(df_target_chr)
    n_h = len(df_hmf_chr)
    n_c = vals_chr.shape[1]

    out = np.zeros((n_t, n_c), dtype=np.float64)

    j = 0
    for i in range(n_t):
        s = t_starts[i]
        e = t_ends[i]
        # advance hmf pointer until h_end > s
        while j < n_h and h_ends[j] <= s:
            j += 1
        k = j
        # accumulate overlaps while h_start < e
        while k < n_h and h_starts[k] < e:
            ovl = min(e, h_ends[k]) - max(s, h_starts[k])
            if ovl > 0:
                weight = ovl / float(h_ends[k] - h_starts[k])  # should be /10000
                out[i, :] += vals_chr[k, :] * weight
            k += 1
    return out


def main():
    print('=== Rebinning SVBP from new_HMF 10KB to match SNV 10KB ===')

    # Load template SNV for coords and column order
    df_snv = pd.read_csv(SNV_10KB)
    target_order = [c for c in df_snv.columns if c not in ['chr', 'start', 'end']]
    cancers_14 = [c for c in target_order if c != 'pancancer']
    print(f'   SNV rows: {len(df_snv):,}; cancers: {len(cancers_14)} + pancancer')

    # Load HMF SVBP and parse coordinates
    df_hmf = pd.read_csv(HMF_SVBP_10KB, sep='\t')
    df_hmf_coords = parse_window_id(df_hmf)

    # Build HMF cancer matrix in target 14 columns
    df_hmf_vals = build_hmf_matrix(df_hmf, cancers_14)

    # Rebin per chromosome
    df_out_parts = []
    for chrom in df_snv['chr'].unique():
        df_t_chr = df_snv[df_snv['chr'] == chrom][['chr', 'start', 'end']].reset_index(drop=True)
        df_h_chr = df_hmf_coords[df_hmf_coords['chr'] == chrom][['start', 'end']].reset_index(drop=True)
        if df_h_chr.empty or df_t_chr.empty:
            continue
        vals_chr = df_hmf_vals.loc[df_h_chr.index].to_numpy(dtype=np.float64)
        rebinned = rebin_chrom(df_t_chr[['start','end']], df_h_chr, vals_chr)
        # round half-up to integers
        rebinned_int = round_half_up(rebinned)
        df_part = df_t_chr.copy()
        for ci, cname in enumerate(cancers_14):
            df_part[cname] = rebinned_int[:, ci]
        df_out_parts.append(df_part)
        print(f'   Re-binned {chrom}: target={len(df_t_chr):,}, src={len(df_h_chr):,}')

    # Concatenate and order exactly like SNV
    df_out = pd.concat(df_out_parts, axis=0, ignore_index=True)
    # Ensure row order exactly matches SNV
    df_out['__key__'] = (
        df_out['chr'].astype(str) + ':' + df_out['start'].astype(str) + '-' + df_out['end'].astype(str)
    )
    df_snv_keys = (
        df_snv['chr'].astype(str) + ':' + df_snv['start'].astype(str) + '-' + df_snv['end'].astype(str)
    )
    df_out = df_out.set_index('__key__').reindex(df_snv_keys).reset_index(drop=True)

    # Compute pancancer
    df_out['pancancer'] = df_out[cancers_14].sum(axis=1).astype(np.int64)

    # Final column order
    final_cols = ['chr', 'start', 'end'] + cancers_14 + ['pancancer']
    df_out = df_out[final_cols]

    # Basic validations
    print('\nValidations:')
    print('   Same shape as SNV:', df_out.shape == df_snv.shape)
    print('   Columns match order:', final_cols == list(df_snv.columns))
    print('   Any negative values:', (df_out[final_cols[3:]] < 0).any().any())

    # Backup and save
    if os.path.exists(SVBP_OUT):
        print('\nBackup existing SVBP 10KB ->', SVBP_BACKUP)
        os.rename(SVBP_OUT, SVBP_BACKUP)
    print('Write new SVBP 10KB ->', SVBP_OUT)
    df_out.to_csv(SVBP_OUT, index=False)

    # Print totals summary
    print('\nTotals:')
    for c in cancers_14[:6]:
        print(f'   {c}: {df_out[c].sum():,}')
    print(f'   pancancer: {df_out["pancancer"].sum():,}')

    print('\nDone.')

if __name__ == '__main__':
    main()

