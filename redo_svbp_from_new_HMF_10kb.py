#!/usr/bin/env python3
"""
Rebuild HMF_svbp_10KB.csv from original new_HMF 10kb TSV so that:
- Coordinates and row order match HMF_snv_10KB.csv exactly
- Columns match the 15 cancer types in HMF_snv_10KB.csv (same order)
- Pancan<PERSON> is the integer sum of the 14 cancer types
- All values are integers (no floats)
"""

import os
import pandas as pd
import numpy as np

SNV_10KB = 'new_ca_rt_mutation/HMF_snv_10KB.csv'
SVBP_HMF_10KB = 'new_HMF/svbp.10000.by_cancer.tsv'
SVBP_OUT = 'new_ca_rt_mutation/HMF_svbp_10KB.csv'
SVBP_BACKUP = 'new_ca_rt_mutation/HMF_svbp_10KB_BACKUP_before_redo.csv'

# Mapping from our standardized cancer types to HMF column names
# Strings map directly; lists are combined (summed)
CANCER_MAPPING = {
    'breast': 'Breast',
    'prostate': 'Prostate',
    'kidney': 'Kidney',  # In this dataset the column is 'Kidney'
    'skin': 'Skin',
    'uterus': ['Endometrium/uterus', 'Cervix'],
    'esophagus': 'Esophagus/gastroesophageal junction',
    'stomach': 'Stomach',
    'nervous_system': 'Brain/central nervous system',
    'lung': 'Lung',
    'colorectal': ['Colon', 'Rectum'],
    'biliary': 'Biliary tract',
    'head_neck': [
        'Hypopharynx', 'Larynx', 'Nasal cavity', 'Nasopharynx',
        'Oral cavity/tongue', 'Oropharynx', 'Salivary gland', 'Thyroid gland'
    ],
    'lymphoid': 'Lymphoid',
    'liver': 'Liver',
}


def build_key_chr_start_end(df: pd.DataFrame) -> pd.Series:
    return df['chr'].astype(str) + ':' + df['start'].astype(str) + '-' + df['end'].astype(str)


def parse_window_id_to_coords(df_hmf: pd.DataFrame) -> pd.DataFrame:
    # window_id like '10:0-10000' (no 'chr' prefix)
    parts = df_hmf['window_id'].astype(str).str.split(':', n=1, expand=True)
    chrom = parts[0]
    start_end = parts[1].str.split('-', n=1, expand=True)
    df_coords = pd.DataFrame({
        'chr': 'chr' + chrom.astype(str),
        'start': start_end[0].astype(np.int64),
        'end': start_end[1].astype(np.int64),
    })
    return df_coords


def main():
    print('=== REDO SVBP FROM new_HMF 10KB (align to SNV structure) ===\n')

    # 1) Load SNV to get target coordinates and column order
    print('📥 Loading SNV 10KB (template for coords and column order)...')
    df_snv = pd.read_csv(SNV_10KB)
    target_cols = [c for c in df_snv.columns if c not in ['chr', 'start', 'end']]
    print(f'   Rows: {len(df_snv):,}, Cancer columns: {len(target_cols)}')

    # 2) Load original HMF SVBP 10kb
    print('\n📥 Loading original HMF SVBP 10KB TSV...')
    df_hmf = pd.read_csv(SVBP_HMF_10KB, sep='\t')
    print(f'   Rows: {len(df_hmf):,}, Columns: {len(df_hmf.columns)}')

    # 3) Parse window_id to coords and build key
    print('\n🔄 Parsing window_id to chr,start,end...')
    df_hmf_coords = parse_window_id_to_coords(df_hmf)
    df_hmf_coords['key'] = build_key_chr_start_end(df_hmf_coords)

    # 4) Build HMF mapped cancer matrix
    print('\n🔧 Mapping HMF columns to 14 standardized cancer types...')
    mapped = pd.DataFrame({'key': df_hmf_coords['key']})

    for std_name in [c for c in target_cols if c != 'pancancer']:
        src = CANCER_MAPPING.get(std_name)
        if src is None:
            # Not expected; create zeros
            mapped[std_name] = 0
            print(f'   ⚠️ No mapping for {std_name}; filled zeros')
            continue
        if isinstance(src, list):
            present_cols = [c for c in src if c in df_hmf.columns]
            if present_cols:
                vals = df_hmf[present_cols].sum(axis=1)
                mapped[std_name] = vals.astype(np.int64)
                print(f'   ✅ {std_name} <= sum({present_cols})')
            else:
                mapped[std_name] = 0
                print(f'   ⚠️ None of {src} found; filled zeros for {std_name}')
        else:
            if src in df_hmf.columns:
                mapped[std_name] = df_hmf[src].astype(np.int64)
                print(f'   ✅ {std_name} <= {src}')
            else:
                mapped[std_name] = 0
                print(f'   ⚠️ {src} not found; filled zeros for {std_name}')

    # 5) Align to SNV coordinate set and order
    print('\n📐 Aligning SVBP rows to SNV 10KB coordinates and order...')
    df_snv_keys = df_snv.copy()
    df_snv_keys['key'] = build_key_chr_start_end(df_snv_keys)

    df_join = df_snv_keys[['chr', 'start', 'end', 'key']].merge(
        mapped, on='key', how='left'
    )

    # Fill missing with zero and cast to int
    for col in [c for c in target_cols if c != 'pancancer']:
        if col in df_join.columns:
            df_join[col] = df_join[col].fillna(0).astype(np.int64)
        else:
            df_join[col] = 0

    # 6) Compute pancancer as integer sum across 14 types
    cancer_14 = [c for c in target_cols if c != 'pancancer']
    df_join['pancancer'] = df_join[cancer_14].sum(axis=1).astype(np.int64)

    # 7) Order columns exactly like SNV
    out_cols = ['chr', 'start', 'end'] + cancer_14 + ['pancancer']
    df_out = df_join[out_cols].copy()

    # 8) Validate
    print('\n✅ Validation:')
    ok_rows = (len(df_out) == len(df_snv))
    ok_cols = (out_cols == list(df_snv.columns))
    print(f'   Rows match SNV: {ok_rows} ({len(df_out):,})')
    print(f'   Columns match SNV order: {ok_cols}')

    # Extra checks: dtypes integers, non-negative
    non_int_cols = [c for c in out_cols[3:] if not pd.api.types.is_integer_dtype(df_out[c])]
    neg_counts = {c: int((df_out[c] < 0).sum()) for c in out_cols[3:]}
    any_neg = any(v > 0 for v in neg_counts.values())
    print(f'   Integer columns: {len(non_int_cols) == 0}')
    print(f'   Any negatives: {any_neg}')

    # 9) Backup and save
    if os.path.exists(SVBP_OUT):
        print('\n📁 Backing up current SVBP:', SVBP_BACKUP)
        os.rename(SVBP_OUT, SVBP_BACKUP)

    print('💾 Saving rebuilt SVBP to:', SVBP_OUT)
    df_out.to_csv(SVBP_OUT, index=False)

    # 10) Summaries
    print('\n📊 Totals (first 5 cancer types):')
    for c in cancer_14[:5]:
        print(f'   {c}: {df_out[c].sum():,}')
    print(f'   pancancer: {df_out["pancancer"].sum():,}')

    print('\n🎉 Done. SVBP 10KB now matches SNV structure and coordinates.')


if __name__ == '__main__':
    main()

