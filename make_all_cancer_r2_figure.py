#!/usr/bin/env python3
"""
Create summary figures from all_cancer_r2_summary.csv
- Three stacked heatmaps (1MB, 100KB, 10KB)
- Rows: cancer types (ctype)
- Columns: mutation types (SNV, INDEL, SVBP)
- Cell value: R^2 (0..~0.85)
- Saves PNG and PDF under ./figures/
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import gridspec

INPUT = 'all_cancer_r2_summary.csv'
OUT_DIR = 'figures'
OUT_PNG = os.path.join(OUT_DIR, 'all_cancer_r2_summary_heatmaps.png')
OUT_PDF = os.path.join(OUT_DIR, 'all_cancer_r2_summary_heatmaps.pdf')

# Order of mutation types and pretty labels
MUTS = ['snv', 'indel', 'svbp']
MUT_LABEL = {'snv': 'SNV', 'indel': 'INDEL', 'svbp': 'SVBP'}

# Resolution blocks and columns per resolution
RESOLUTIONS = [
    ('1MB', ['1mb_snv', '1mb_indel', '1mb_svbp']),
    ('100KB', ['100kb_snv', '100kb_indel', '100kb_svbp']),
    ('10KB', ['10kb_snv', '10kb_indel', '10kb_svbp']),
]


def load_data(path: str) -> pd.DataFrame:
    df = pd.read_csv(path)
    # Drop blank trailing line if present
    df = df.dropna(how='all')
    return df


def build_matrix(df: pd.DataFrame, cols: list) -> np.ndarray:
    """Return a 2D matrix (n_ctypes x 3) for a given resolution column set."""
    return df[cols].to_numpy(dtype=float)


def make_heatmaps(df: pd.DataFrame):
    os.makedirs(OUT_DIR, exist_ok=True)

    # Define y-axis (cancer types) order as they appear in file
    ctypes = df['ctype'].tolist()

    # Figure layout
    nrows = len(RESOLUTIONS)
    ncols = 1
    height_per_row = max(0.3 * len(ctypes), 2.5)  # scale height with number of rows
    fig_height = min(height_per_row * nrows / 2.0 + 2, 18)  # cap to avoid too tall
    fig_width = 8

    fig = plt.figure(figsize=(fig_width, fig_height), dpi=160)
    gs = gridspec.GridSpec(nrows, ncols, height_ratios=[1, 1, 1], hspace=0.25)

    # Color scale range
    vmin, vmax = 0.0, 0.9
    cmap = 'viridis'

    # Plot each resolution as a heatmap
    for i, (title, cols) in enumerate(RESOLUTIONS):
        ax = fig.add_subplot(gs[i, 0])
        mat = build_matrix(df, cols)
        im = ax.imshow(mat, aspect='auto', interpolation='nearest', cmap=cmap, vmin=vmin, vmax=vmax)

        # Y labels (ctypes)
        ax.set_yticks(range(len(ctypes)))
        ax.set_yticklabels(ctypes, fontsize=9)

        # X labels (mutation types)
        ax.set_xticks(range(3))
        ax.set_xticklabels([MUT_LABEL[m] for m in MUTS], fontsize=10)

        # Title and grid lines
        ax.set_title(f'{title}', fontsize=12, pad=8)
        ax.grid(False)

        # Annotate with values (optional; keep small to reduce clutter)
        for r in range(mat.shape[0]):
            for c in range(mat.shape[1]):
                val = mat[r, c]
                # Only annotate if not nearly zero to avoid cluttering zeros
                if not np.isnan(val) and (val >= 0.02 or val <= -0.02):
                    ax.text(c, r, f'{val:.2f}', ha='center', va='center', color='white' if val > 0.45 else 'black', fontsize=7)

        # Add colorbar only on the right side for the bottom plot
        if i == nrows - 1:
            cbar = fig.colorbar(im, ax=ax, fraction=0.046, pad=0.04)
            cbar.set_label('R²', rotation=0, labelpad=10)

    # Overall title
    fig.suptitle('All Cancer R² Summary by Resolution and Mutation Type', fontsize=14, y=0.995)

    # Save
    fig.savefig(OUT_PNG, bbox_inches='tight')
    fig.savefig(OUT_PDF, bbox_inches='tight')
    plt.close(fig)

    return OUT_PNG, OUT_PDF


def main():
    df = load_data(INPUT)
    png, pdf = make_heatmaps(df)
    print('Saved figure:')
    print('  ', png)
    print('  ', pdf)


if __name__ == '__main__':
    main()

