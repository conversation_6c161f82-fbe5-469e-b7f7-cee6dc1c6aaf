#!/usr/bin/env Rscript
# Check multi-task values for discrepancies

library(dplyr)
library(readr)
library(tidyr)

# Load the original multi-task results
multi_results <- read_csv("best_models_r2_wide.csv")

# Extract the 6 cancer types
target_cancers <- c("breast", "lung", "colorectal", "esophagus", "prostate", "skin")
multi_filtered <- multi_results %>%
  filter(cancer %in% target_cancers) %>%
  select(cancer, snv_r2_1mb, snv_r2_100kb, snv_r2_10kb, 
         indel_r2_1mb, indel_r2_100kb, indel_r2_10kb) %>%
  rename(cancer_type = cancer)

# Load your edited values
edited_data <- read_csv("figures/all_r2_values_for_editing.csv")

# Compare multi-task values
comparison <- edited_data %>%
  select(cancer_type, mutation_type, resolution, multi_task_R2) %>%
  pivot_wider(names_from = c(mutation_type, resolution), 
              values_from = multi_task_R2,
              names_sep = "_") %>%
  arrange(match(cancer_type, c("breast", "lung", "colorectal", "esophagus", "prostate", "skin")))

# Create correct multi-task table
correct_multi <- data.frame(
  cancer_type = c("breast", "lung", "colorectal", "esophagus", "prostate", "skin"),
  SNV_1MB = multi_filtered$snv_r2_1mb,
  SNV_100KB = multi_filtered$snv_r2_100kb,
  SNV_10KB = multi_filtered$snv_r2_10kb,
  INDEL_1MB = multi_filtered$indel_r2_1mb,
  INDEL_100KB = multi_filtered$indel_r2_100kb,
  INDEL_10KB = multi_filtered$indel_r2_10kb
)

cat("=== COMPARISON: WHAT WE'VE BEEN USING vs ACTUAL VALUES ===\n\n")

cat("What we've been using:\n")
print(comparison)

cat("\nActual values from best_models_r2_wide.csv:\n")
print(correct_multi)

cat("\n=== DISCREPANCIES FOUND ===\n")

# Check for discrepancies
discrepancies <- data.frame(
  cancer_type = character(),
  mutation_type = character(),
  resolution = character(),
  used_value = numeric(),
  correct_value = numeric(),
  difference = numeric(),
  stringsAsFactors = FALSE
)

# Check each value
for(i in 1:nrow(correct_multi)) {
  cancer <- correct_multi$cancer_type[i]
  
  # SNV values
  if(abs(comparison$SNV_1MB[i] - correct_multi$SNV_1MB[i]) > 0.001) {
    discrepancies <- rbind(discrepancies, data.frame(
      cancer_type = cancer, mutation_type = "SNV", resolution = "1MB",
      used_value = comparison$SNV_1MB[i], correct_value = correct_multi$SNV_1MB[i],
      difference = correct_multi$SNV_1MB[i] - comparison$SNV_1MB[i]
    ))
  }
  
  if(abs(comparison$SNV_100KB[i] - correct_multi$SNV_100KB[i]) > 0.001) {
    discrepancies <- rbind(discrepancies, data.frame(
      cancer_type = cancer, mutation_type = "SNV", resolution = "100KB",
      used_value = comparison$SNV_100KB[i], correct_value = correct_multi$SNV_100KB[i],
      difference = correct_multi$SNV_100KB[i] - comparison$SNV_100KB[i]
    ))
  }
  
  if(abs(comparison$SNV_10KB[i] - correct_multi$SNV_10KB[i]) > 0.001) {
    discrepancies <- rbind(discrepancies, data.frame(
      cancer_type = cancer, mutation_type = "SNV", resolution = "10KB",
      used_value = comparison$SNV_10KB[i], correct_value = correct_multi$SNV_10KB[i],
      difference = correct_multi$SNV_10KB[i] - comparison$SNV_10KB[i]
    ))
  }
  
  # INDEL values
  if(abs(comparison$INDEL_1MB[i] - correct_multi$INDEL_1MB[i]) > 0.001) {
    discrepancies <- rbind(discrepancies, data.frame(
      cancer_type = cancer, mutation_type = "INDEL", resolution = "1MB",
      used_value = comparison$INDEL_1MB[i], correct_value = correct_multi$INDEL_1MB[i],
      difference = correct_multi$INDEL_1MB[i] - comparison$INDEL_1MB[i]
    ))
  }
  
  if(abs(comparison$INDEL_100KB[i] - correct_multi$INDEL_100KB[i]) > 0.001) {
    discrepancies <- rbind(discrepancies, data.frame(
      cancer_type = cancer, mutation_type = "INDEL", resolution = "100KB",
      used_value = comparison$INDEL_100KB[i], correct_value = correct_multi$INDEL_100KB[i],
      difference = correct_multi$INDEL_100KB[i] - comparison$INDEL_100KB[i]
    ))
  }
  
  if(abs(comparison$INDEL_10KB[i] - correct_multi$INDEL_10KB[i]) > 0.001) {
    discrepancies <- rbind(discrepancies, data.frame(
      cancer_type = cancer, mutation_type = "INDEL", resolution = "10KB",
      used_value = comparison$INDEL_10KB[i], correct_value = correct_multi$INDEL_10KB[i],
      difference = correct_multi$INDEL_10KB[i] - comparison$INDEL_10KB[i]
    ))
  }
}

if(nrow(discrepancies) > 0) {
  print(discrepancies)
  cat("\n⚠️  Found", nrow(discrepancies), "discrepancies!\n")
} else {
  cat("✅ No discrepancies found - all values match!\n")
}

# Save corrected values
write_csv(correct_multi, "figures/correct_multitask_values.csv")
cat("\n📁 Saved correct values to: figures/correct_multitask_values.csv\n")
