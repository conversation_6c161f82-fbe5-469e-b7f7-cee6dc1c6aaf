import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns

# Read the data files
rf_baseline = pd.read_csv('figures/RF_baseline_R__summary.csv')
multitask_results = pd.read_csv('figures/corrected_final_r2_values.csv')

# Process RF baseline data
# Calculate average SNV and INDEL r^2 scores across all cancer types and scales
rf_snv_avg = rf_baseline[rf_baseline['task'] == 'snv']['r2_rf'].mean()
rf_indel_avg = rf_baseline[rf_baseline['task'] == 'indel']['r2_rf'].mean()

# Process multitask results
# Calculate average SNV and INDEL r^2 scores across all cancer types and scales
multitask_snv_avg = multitask_results[multitask_results['mutation_type'] == 'SNV']['multi_task_R2'].mean()
multitask_indel_avg = multitask_results[multitask_results['mutation_type'] == 'INDEL']['multi_task_R2'].mean()

print("RF Baseline Results:")
print(f"Average SNV r^2: {rf_snv_avg:.4f}")
print(f"Average INDEL r^2: {rf_indel_avg:.4f}")

print("\nMultitask Model Results:")
print(f"Average SNV r^2: {multitask_snv_avg:.4f}")
print(f"Average INDEL r^2: {multitask_indel_avg:.4f}")

# Prepare data for per-cancer type comparison
# Calculate average r² scores by cancer type for each model
rf_by_cancer = rf_baseline.groupby(['ctype', 'task'])['r2_rf'].mean().reset_index()
multitask_by_cancer = multitask_results.groupby(['cancer_type', 'mutation_type'])['multi_task_R2'].mean().reset_index()

# Rename columns for consistency
rf_by_cancer = rf_by_cancer.rename(columns={'ctype': 'cancer_type', 'task': 'mutation_type', 'r2_rf': 'r2_score'})
rf_by_cancer['mutation_type'] = rf_by_cancer['mutation_type'].str.upper()
rf_by_cancer['model'] = 'RF Baseline'

multitask_by_cancer = multitask_by_cancer.rename(columns={'multi_task_R2': 'r2_score'})
multitask_by_cancer['model'] = 'Multitask'

# Combine data
combined_by_cancer = pd.concat([rf_by_cancer, multitask_by_cancer], ignore_index=True)

# Create per-cancer type comparison figure
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 12))

# Get unique cancer types
cancer_types = sorted(combined_by_cancer['cancer_type'].unique())

# SNV comparison
snv_data = combined_by_cancer[combined_by_cancer['mutation_type'] == 'SNV']
snv_rf = snv_data[snv_data['model'] == 'RF Baseline'].set_index('cancer_type')['r2_score']
snv_multitask = snv_data[snv_data['model'] == 'Multitask'].set_index('cancer_type')['r2_score']

x = np.arange(len(cancer_types))
width = 0.35

bars1 = ax1.bar(x - width/2, [snv_rf.get(ct, 0) for ct in cancer_types], width, 
                label='RF Baseline', alpha=0.8, color='lightcoral')
bars2 = ax1.bar(x + width/2, [snv_multitask.get(ct, 0) for ct in cancer_types], width, 
                label='Multitask Model', alpha=0.8, color='skyblue')

ax1.set_xlabel('Cancer Type')
ax1.set_ylabel('Average R² Score')
ax1.set_title('SNV R² Comparison by Cancer Type: RF Baseline vs Multitask Model')
ax1.set_xticks(x)
ax1.set_xticklabels(cancer_types, rotation=45, ha='right')
ax1.legend()
ax1.grid(True, alpha=0.3)

# Add value labels on SNV bars
for bar in bars1:
    height = bar.get_height()
    if height > 0:
        ax1.annotate(f'{height:.3f}',
                    xy=(bar.get_x() + bar.get_width() / 2, height),
                    xytext=(0, 3),
                    textcoords="offset points",
                    ha='center', va='bottom', fontsize=9)

for bar in bars2:
    height = bar.get_height()
    if height > 0:
        ax1.annotate(f'{height:.3f}',
                    xy=(bar.get_x() + bar.get_width() / 2, height),
                    xytext=(0, 3),
                    textcoords="offset points",
                    ha='center', va='bottom', fontsize=9)

# INDEL comparison
indel_data = combined_by_cancer[combined_by_cancer['mutation_type'] == 'INDEL']
indel_rf = indel_data[indel_data['model'] == 'RF Baseline'].set_index('cancer_type')['r2_score']
indel_multitask = indel_data[indel_data['model'] == 'Multitask'].set_index('cancer_type')['r2_score']

bars3 = ax2.bar(x - width/2, [indel_rf.get(ct, 0) for ct in cancer_types], width, 
                label='RF Baseline', alpha=0.8, color='lightcoral')
bars4 = ax2.bar(x + width/2, [indel_multitask.get(ct, 0) for ct in cancer_types], width, 
                label='Multitask Model', alpha=0.8, color='skyblue')

ax2.set_xlabel('Cancer Type')
ax2.set_ylabel('Average R² Score')
ax2.set_title('INDEL R² Comparison by Cancer Type: RF Baseline vs Multitask Model')
ax2.set_xticks(x)
ax2.set_xticklabels(cancer_types, rotation=45, ha='right')
ax2.legend()
ax2.grid(True, alpha=0.3)

# Add value labels on INDEL bars
for bar in bars3:
    height = bar.get_height()
    if height > 0:
        ax2.annotate(f'{height:.3f}',
                    xy=(bar.get_x() + bar.get_width() / 2, height),
                    xytext=(0, 3),
                    textcoords="offset points",
                    ha='center', va='bottom', fontsize=9)

for bar in bars4:
    height = bar.get_height()
    if height > 0:
        ax2.annotate(f'{height:.3f}',
                    xy=(bar.get_x() + bar.get_width() / 2, height),
                    xytext=(0, 3),
                    textcoords="offset points",
                    ha='center', va='bottom', fontsize=9)

plt.tight_layout()
plt.savefig('figures/per_cancer_baseline_vs_multitask_comparison.png', dpi=300, bbox_inches='tight')
plt.savefig('figures/per_cancer_baseline_vs_multitask_comparison.pdf', bbox_inches='tight')
plt.show()

# Also create overall average comparison for reference
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))

# Bar plot comparing overall averages
categories = ['SNV', 'INDEL']
rf_values = [rf_snv_avg, rf_indel_avg]
multitask_values = [multitask_snv_avg, multitask_indel_avg]

x_avg = np.arange(len(categories))
width = 0.35

bars1 = ax1.bar(x_avg - width/2, rf_values, width, label='RF Baseline', alpha=0.8, color='lightcoral')
bars2 = ax1.bar(x_avg + width/2, multitask_values, width, label='Multitask Model', alpha=0.8, color='skyblue')

ax1.set_xlabel('Mutation Type')
ax1.set_ylabel('Average R² Score')
ax1.set_title('Overall Average R² Comparison: RF Baseline vs Multitask Model')
ax1.set_xticks(x_avg)
ax1.set_xticklabels(categories)
ax1.legend()
ax1.grid(True, alpha=0.3)

# Add value labels on bars
for bar in bars1:
    height = bar.get_height()
    ax1.annotate(f'{height:.3f}',
                xy=(bar.get_x() + bar.get_width() / 2, height),
                xytext=(0, 3),
                textcoords="offset points",
                ha='center', va='bottom', fontsize=10)

for bar in bars2:
    height = bar.get_height()
    ax1.annotate(f'{height:.3f}',
                xy=(bar.get_x() + bar.get_width() / 2, height),
                xytext=(0, 3),
                textcoords="offset points",
                ha='center', va='bottom', fontsize=10)

# Improvement plot
improvements = [multitask_snv_avg - rf_snv_avg, multitask_indel_avg - rf_indel_avg]
colors = ['green' if imp > 0 else 'red' for imp in improvements]

bars3 = ax2.bar(categories, improvements, color=colors, alpha=0.7)
ax2.set_xlabel('Mutation Type')
ax2.set_ylabel('R² Improvement (Multitask - RF Baseline)')
ax2.set_title('Overall R² Improvement: Multitask vs RF Baseline')
ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
ax2.grid(True, alpha=0.3)

# Add value labels on improvement bars
for bar, imp in zip(bars3, improvements):
    height = bar.get_height()
    ax2.annotate(f'{imp:.3f}',
                xy=(bar.get_x() + bar.get_width() / 2, height),
                xytext=(0, 3 if height > 0 else -15),
                textcoords="offset points",
                ha='center', va='bottom' if height > 0 else 'top', fontsize=10)

plt.tight_layout()
plt.savefig('figures/overall_baseline_vs_multitask_comparison.png', dpi=300, bbox_inches='tight')
plt.savefig('figures/overall_baseline_vs_multitask_comparison.pdf', bbox_inches='tight')
plt.show()

# Create detailed comparison by cancer type and scale
print("\n" + "="*60)
print("DETAILED COMPARISON BY CANCER TYPE AND SCALE")
print("="*60)

# Prepare data for detailed comparison
rf_detailed = rf_baseline.copy()
rf_detailed['model'] = 'RF Baseline'
rf_detailed = rf_detailed.rename(columns={'ctype': 'cancer_type', 'task': 'mutation_type', 'r2_rf': 'r2_score'})
rf_detailed['mutation_type'] = rf_detailed['mutation_type'].str.upper()

multitask_detailed = multitask_results.copy()
multitask_detailed['model'] = 'Multitask'
multitask_detailed['scale'] = multitask_detailed['resolution'].str.lower()
multitask_detailed = multitask_detailed.rename(columns={'multi_task_R2': 'r2_score'})
multitask_detailed = multitask_detailed[['cancer_type', 'mutation_type', 'scale', 'r2_score', 'model']]

# Combine datasets
combined_data = pd.concat([rf_detailed[['cancer_type', 'mutation_type', 'scale', 'r2_score', 'model']], 
                          multitask_detailed], ignore_index=True)

# Create detailed heatmap comparison
fig, axes = plt.subplots(2, 2, figsize=(16, 12))

for i, mut_type in enumerate(['SNV', 'INDEL']):
    for j, model in enumerate(['RF Baseline', 'Multitask']):
        data_subset = combined_data[(combined_data['mutation_type'] == mut_type) & 
                                   (combined_data['model'] == model)]
        
        # Pivot for heatmap
        heatmap_data = data_subset.pivot(index='cancer_type', columns='scale', values='r2_score')
        
        # Reorder columns to match typical scale order
        scale_order = ['10kb', '100kb', '1mb']
        heatmap_data = heatmap_data.reindex(columns=scale_order)
        
        sns.heatmap(heatmap_data, annot=True, fmt='.3f', cmap='RdYlBu_r', 
                   ax=axes[i, j], cbar_kws={'label': 'R² Score'})
        axes[i, j].set_title(f'{mut_type} - {model}')
        axes[i, j].set_xlabel('Scale')
        axes[i, j].set_ylabel('Cancer Type')

plt.tight_layout()
plt.savefig('figures/detailed_baseline_vs_multitask_heatmaps.png', dpi=300, bbox_inches='tight')
plt.savefig('figures/detailed_baseline_vs_multitask_heatmaps.pdf', bbox_inches='tight')
plt.show()

# Summary statistics
print(f"\nSUMMARY STATISTICS:")
print(f"RF Baseline - SNV Average: {rf_snv_avg:.4f}")
print(f"RF Baseline - INDEL Average: {rf_indel_avg:.4f}")
print(f"Multitask - SNV Average: {multitask_snv_avg:.4f}")
print(f"Multitask - INDEL Average: {multitask_indel_avg:.4f}")
print(f"SNV Improvement: {multitask_snv_avg - rf_snv_avg:.4f} ({((multitask_snv_avg - rf_snv_avg)/rf_snv_avg)*100:.2f}%)")
print(f"INDEL Improvement: {multitask_indel_avg - rf_indel_avg:.4f} ({((multitask_indel_avg - rf_indel_avg)/rf_indel_avg)*100:.2f}%)")
